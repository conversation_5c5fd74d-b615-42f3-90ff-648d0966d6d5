import { describe, it, expect } from 'vitest';
import { 
  interpolateTemplate, 
  interpolate,
  validateTemplate,
  extractTemplateVariables,
  getAttributeValue,
  renderAttributesList,
  createInterpolationContext
} from '../template-utils';
import type { AttributeDict, AttributeListConfig } from '@/types/templates';

describe('template-utils', () => {
  describe('interpolateTemplate', () => {
    it('should interpolate simple variables', () => {
      const template = 'Hello {{data.name}}!';
      const context = {
        data: { name: 'World' },
        attr: {}
      };

      const result = interpolateTemplate(template, context);
      
      expect(result.content).toBe('Hello World!');
      expect(result.hasErrors).toBe(false);
      expect(result.errors).toHaveLength(0);
    });

    it('should interpolate nested variables', () => {
      const template = 'Category: {{data.category.name}}';
      const context = {
        data: { category: { name: 'Engine Parts' } },
        attr: {}
      };

      const result = interpolateTemplate(template, context);
      
      expect(result.content).toBe('Category: Engine Parts');
      expect(result.hasErrors).toBe(false);
    });

    it('should interpolate attribute variables', () => {
      const template = 'Diameter: {{attr.diameter}}';
      const context = {
        data: {},
        attr: { diameter: { value: '50mm', unit: 'mm' } }
      };

      const result = interpolateTemplate(template, context);
      
      expect(result.content).toBe('Diameter: 50mm');
      expect(result.hasErrors).toBe(false);
    });

    it('should handle missing variables gracefully', () => {
      const template = 'Hello {{data.missing}}!';
      const context = {
        data: {},
        attr: {}
      };

      const result = interpolateTemplate(template, context);
      
      expect(result.content).toBe('Hello !');
      expect(result.hasErrors).toBe(false);
    });

    it('should handle empty template', () => {
      const result = interpolateTemplate('', { data: {}, attr: {} });
      
      expect(result.content).toBe('');
      expect(result.hasErrors).toBe(false);
    });
  });

  describe('interpolate', () => {
    it('should return only content', () => {
      const template = 'Hello {{data.name}}!';
      const context = {
        data: { name: 'World' },
        attr: {}
      };

      const result = interpolate(template, context);
      
      expect(result).toBe('Hello World!');
    });
  });

  describe('validateTemplate', () => {
    it('should validate correct template', () => {
      const result = validateTemplate('Hello {{data.name}}!');
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect mismatched braces', () => {
      const result = validateTemplate('Hello {{data.name}!');
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Несоответствие количества открывающих и закрывающих скобок');
    });

    it('should detect empty expressions', () => {
      const result = validateTemplate('Hello {{}}!');
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Найдено пустое выражение {{}}');
    });

    it('should detect invalid expressions', () => {
      const result = validateTemplate('Hello {{data.name-invalid}}!');
      
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe('extractTemplateVariables', () => {
    it('should extract variables from template', () => {
      const template = 'Hello {{data.name}} with {{attr.diameter}}!';
      const variables = extractTemplateVariables(template);
      
      expect(variables).toEqual(['data.name', 'attr.diameter']);
    });

    it('should handle duplicate variables', () => {
      const template = 'Hello {{data.name}} and {{data.name}} again!';
      const variables = extractTemplateVariables(template);
      
      expect(variables).toEqual(['data.name']);
    });

    it('should handle empty template', () => {
      const variables = extractTemplateVariables('');
      
      expect(variables).toEqual([]);
    });
  });

  describe('getAttributeValue', () => {
    const attr: AttributeDict = {
      diameter: { value: '50', unit: 'mm' },
      material: { value: 'Steel' }
    };

    it('should get attribute value without unit', () => {
      const result = getAttributeValue('diameter', attr, false);
      
      expect(result).toBe('50');
    });

    it('should get attribute value with unit', () => {
      const result = getAttributeValue('diameter', attr, true);
      
      expect(result).toBe('50 mm');
    });

    it('should handle missing attribute', () => {
      const result = getAttributeValue('missing', attr, false);
      
      expect(result).toBe('—');
    });

    it('should handle attribute without unit', () => {
      const result = getAttributeValue('material', attr, true);
      
      expect(result).toBe('Steel');
    });
  });

  describe('renderAttributesList', () => {
    const config: AttributeListConfig = {
      attributeNames: ['diameter', 'material', 'weight'],
      sortOrder: ['material', 'diameter'],
      withUnits: true
    };

    const attr: AttributeDict = {
      diameter: { value: '50', unit: 'mm', template: { name: 'diameter', title: 'Диаметр', unit: 'mm' } },
      material: { value: 'Steel', template: { name: 'material', title: 'Материал' } },
      weight: { value: '2.5', unit: 'kg', template: { name: 'weight', title: 'Вес', unit: 'kg' } }
    };

    it('should render attributes list', () => {
      const result = renderAttributesList(config, attr);
      
      expect(result).toHaveLength(3);
      expect(result[0].name).toBe('material'); // Первый по sortOrder
      expect(result[1].name).toBe('diameter'); // Второй по sortOrder
      expect(result[2].name).toBe('weight');   // Остальные по алфавиту
    });

    it('should include units when configured', () => {
      const result = renderAttributesList(config, attr);
      
      const diameterAttr = result.find(a => a.name === 'diameter');
      expect(diameterAttr?.value).toBe('50 mm');
      
      const materialAttr = result.find(a => a.name === 'material');
      expect(materialAttr?.value).toBe('Steel'); // Нет единицы измерения
    });

    it('should handle missing attributes', () => {
      const partialAttr: AttributeDict = {
        diameter: { value: '50', unit: 'mm', template: { name: 'diameter', title: 'Диаметр', unit: 'mm' } }
      };

      const result = renderAttributesList(config, partialAttr);
      
      expect(result).toHaveLength(1); // Только diameter
      expect(result[0].name).toBe('diameter');
    });
  });

  describe('createInterpolationContext', () => {
    it('should create context from data and attributes', () => {
      const data = { category: { name: 'Engine' } };
      const attr: AttributeDict = {
        diameter: { value: '50', unit: 'mm' }
      };

      const context = createInterpolationContext(data, attr);
      
      expect(context.data).toBe(data);
      expect(context.attr).toBe(attr);
    });
  });
});
