"use client";

import React from 'react';
import type { CatalogItemTemplateRendererProps } from '@/types/templates';
import { 
  interpolate, 
  createInterpolationContext, 
  renderAttributesList,
  createFallbackContent 
} from '@/lib/template-utils';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Building, Package, ExternalLink } from 'lucide-react';

/**
 * Компонент для рендеринга страниц каталожных позиций на основе шаблонов
 */
export function CatalogItemTemplateRenderer({ config, data, attr }: CatalogItemTemplateRendererProps) {
  const context = createInterpolationContext(data, attr);
  const item = data.item;
  const brand = data.brand;

  // Интерполируем заголовки
  const h1 = config.h1 
    ? interpolate(config.h1, context)
    : createFallbackContent('h1', item.sku, 'catalogItem');

  const h2 = config.h2 
    ? interpolate(config.h2, context)
    : createFallbackContent('h2', item.sku, 'catalogItem');

  // Рендерим атрибуты согласно конфигурации
  const attributes = renderAttributesList(config.attributes, attr);

  return (
    <div className="space-y-6">
      {/* Основной заголовок */}
      <div className="space-y-2">
        <div className="flex items-center gap-3">
          <h1 className="text-3xl font-bold tracking-tight font-mono">{h1}</h1>
          {brand.isOem && (
            <Badge variant="secondary" className="text-xs">
              OEM
            </Badge>
          )}
        </div>
        {h2 && h2 !== h1 && (
          <h2 className="text-xl text-muted-foreground">{h2}</h2>
        )}
      </div>

      {/* Информация о бренде */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-3">
            <Building className="h-5 w-5 text-muted-foreground" />
            <div>
              <p className="font-semibold">{brand.name}</p>
              <p className="text-sm text-muted-foreground">
                {brand.isOem ? 'Оригинальный производитель (OEM)' : 'Производитель запчастей (Aftermarket)'}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Основная информация */}
        <div className="lg:col-span-2 space-y-6">
          {/* Изображение */}
          {item.image && (
            <Card>
              <CardContent className="p-6">
                <div className="relative w-full h-64 md:h-80 rounded-lg overflow-hidden">
                  <img
                    src={item.image.url}
                    alt={item.image.alt || item.sku}
                    className="w-full h-full object-cover"
                  />
                </div>
              </CardContent>
            </Card>
          )}

          {/* Описание */}
          {item.description && (
            <Card>
              <CardHeader>
                <CardTitle>Описание</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground leading-relaxed">{item.description}</p>
              </CardContent>
            </Card>
          )}

          {/* Технические характеристики */}
          {attributes.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Технические характеристики</CardTitle>
                <CardDescription>
                  Параметры и свойства артикула от производителя
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {attributes.map((attr) => (
                    <div key={attr.name} className="flex justify-between items-center py-2 border-b border-border/50 last:border-b-0">
                      <span className="text-sm font-medium">{attr.title}</span>
                      <span className="text-sm text-muted-foreground font-mono">{attr.value}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Дополнительные медиафайлы */}
          {item.mediaAssets && item.mediaAssets.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Дополнительные материалы</CardTitle>
                <CardDescription>
                  Каталоги, схемы и техническая документация
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {item.mediaAssets.map((media) => (
                    <div key={media.id} className="relative group">
                      {media.type.startsWith('image/') ? (
                        <img
                          src={media.url}
                          alt={media.alt || 'Дополнительное изображение'}
                          className="w-full h-24 object-cover rounded-lg border"
                        />
                      ) : (
                        <div className="w-full h-24 bg-muted rounded-lg border flex items-center justify-center">
                          <span className="text-xs text-muted-foreground">
                            {media.type.split('/')[1]?.toUpperCase() || 'FILE'}
                          </span>
                        </div>
                      )}
                      <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                        <Button size="sm" variant="secondary">
                          <ExternalLink className="h-3 w-3 mr-1" />
                          Открыть
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Боковая панель */}
        <div className="space-y-6">
          {/* Информация об артикуле */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Package className="h-4 w-4" />
                Артикул
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <span className="text-sm font-medium text-muted-foreground">SKU:</span>
                <p className="text-lg font-mono font-bold mt-1">{item.sku}</p>
              </div>

              <div>
                <span className="text-sm font-medium text-muted-foreground">Производитель:</span>
                <div className="flex items-center gap-2 mt-1">
                  <Badge variant={brand.isOem ? "default" : "outline"}>
                    {brand.name}
                  </Badge>
                  {brand.isOem && (
                    <Badge variant="secondary" className="text-xs">
                      OEM
                    </Badge>
                  )}
                </div>
              </div>

              {item.source && (
                <div>
                  <span className="text-sm font-medium text-muted-foreground">Источник данных:</span>
                  <p className="text-sm mt-1">{item.source}</p>
                </div>
              )}

              <div>
                <span className="text-sm font-medium text-muted-foreground">ID позиции:</span>
                <p className="text-sm mt-1 font-mono">#{item.id}</p>
              </div>
            </CardContent>
          </Card>

          {/* Действия */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Действия</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button className="w-full" variant="default">
                Найти аналоги
              </Button>
              <Button className="w-full" variant="outline">
                Показать группы
              </Button>
              <Button className="w-full" variant="outline">
                Сравнить с другими
              </Button>
              <Button className="w-full" variant="outline">
                Экспорт данных
              </Button>
            </CardContent>
          </Card>

          {/* Статистика */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Статистика</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Атрибутов:</span>
                <span className="text-sm font-semibold">{item.attributes.length}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Медиафайлов:</span>
                <span className="text-sm font-semibold">{item.mediaAssets?.length || 0}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Тип производителя:</span>
                <span className="text-sm font-semibold">{brand.isOem ? 'OEM' : 'Aftermarket'}</span>
              </div>
            </CardContent>
          </Card>

          {/* Дополнительная информация */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Справка</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2 text-sm text-muted-foreground">
              <p>
                <strong>OEM</strong> - оригинальные запчасти от производителя техники
              </p>
              <p>
                <strong>Aftermarket</strong> - запчасти от сторонних производителей
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

/**
 * Компонент страницы каталожной позиции с поддержкой шаблонов
 */
export function CatalogItemPageRenderer({ renderData }: { renderData: any }) {
  if (!renderData) {
    return (
      <div className="text-center py-12">
        <h1 className="text-2xl font-bold text-muted-foreground">Артикул не найден</h1>
        <p className="text-muted-foreground mt-2">
          Запрашиваемый артикул не существует или был удален.
        </p>
      </div>
    );
  }

  // Если есть шаблон, используем его
  if (renderData.template && renderData.template.config) {
    return (
      <CatalogItemTemplateRenderer
        config={renderData.template.config}
        data={renderData.data}
        attr={renderData.attr}
      />
    );
  }

  // Fallback рендеринг без шаблона
  const item = renderData.item;
  
  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight font-mono">{item.sku}</h1>
        <div className="flex items-center gap-2">
          <Badge variant="outline">{item.brand.name}</Badge>
          {item.brand.isOem && (
            <Badge variant="secondary">OEM</Badge>
          )}
        </div>
      </div>

      {item.image && (
        <div className="relative w-full h-48 md:h-64 rounded-lg overflow-hidden">
          <img
            src={item.image.url}
            alt={item.image.alt || item.sku}
            className="w-full h-full object-cover"
          />
        </div>
      )}

      {item.description && (
        <Card>
          <CardHeader>
            <CardTitle>Описание</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">{item.description}</p>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Информация об артикуле</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <span className="text-sm font-medium text-muted-foreground">Производитель:</span>
              <p className="text-lg font-semibold">{item.brand.name}</p>
            </div>
            <div>
              <span className="text-sm font-medium text-muted-foreground">Тип:</span>
              <p className="text-lg font-semibold">{item.brand.isOem ? 'OEM' : 'Aftermarket'}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
