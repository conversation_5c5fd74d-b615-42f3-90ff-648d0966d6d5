---
import MainLayout from "../../../layouts/MainLayout.astro";
import { trpcClient } from "@/lib/trpc";
import { TrpcProvider } from "@/components/providers/TrpcProvider";
import CategoryTemplateIsland from "@/components/templates/islands/CategoryTemplateIsland";
import { generateCategorySEO, generateStructuredDataScript } from "@/lib/seo-utils";

const { slug } = Astro.params;

if (!slug) {
  return Astro.redirect('/categories');
}

// Загружаем данные для рендеринга шаблона категории
let renderData: any = null;
let category: any = null;

try {
  // Получаем данные для рендеринга через специальный роут
  renderData = await trpcClient.pageTemplates.renderCategory.query({ slug });
  category = renderData?.category;

  if (!category) {
    return Astro.redirect('/categories');
  }

} catch (error) {
  console.error('Error loading category template data:', error);
  return Astro.redirect('/categories');
}

// Генерируем SEO данные
const seoData = generateCategorySEO(renderData);
const structuredDataScript = generateStructuredDataScript(seoData.structuredData);
---

<MainLayout title={seoData.title} description={seoData.description}>
  <Fragment slot="head">
    <meta name="keywords" content={seoData.keywords?.join(', ')} />
    {seoData.openGraph && (
      <>
        <meta property="og:title" content={seoData.openGraph.title} />
        <meta property="og:description" content={seoData.openGraph.description} />
        <meta property="og:type" content={seoData.openGraph.type} />
        {seoData.openGraph.image && <meta property="og:image" content={seoData.openGraph.image} />}
      </>
    )}
    <Fragment set:html={structuredDataScript} />
  </Fragment>
  <TrpcProvider client:load>
    <div class="container mx-auto px-4 py-8">
      <!-- Хлебные крошки -->
      <nav class="flex mb-8" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
          <li class="inline-flex items-center">
            <a href="/" class="inline-flex items-center text-sm font-medium text-muted-foreground hover:text-foreground">
              Главная
            </a>
          </li>
          <li>
            <div class="flex items-center">
              <span class="mx-2 text-muted-foreground">/</span>
              <a href="/categories" class="text-sm font-medium text-muted-foreground hover:text-foreground">Категории</a>
            </div>
          </li>
          {category.parent && (
            <li>
              <div class="flex items-center">
                <span class="mx-2 text-muted-foreground">/</span>
                <a href={`/catalog/categories/${category.parent.slug}`} class="text-sm font-medium text-muted-foreground hover:text-foreground">
                  {category.parent.name}
                </a>
              </div>
            </li>
          )}
          <li aria-current="page">
            <div class="flex items-center">
              <span class="mx-2 text-muted-foreground">/</span>
              <span class="text-sm font-medium text-foreground">{category.name}</span>
            </div>
          </li>
        </ol>
      </nav>

      <!-- Рендеринг через систему шаблонов -->
      <CategoryTemplateIsland client:load renderData={renderData} />
    </div>
  </TrpcProvider>
</MainLayout>
