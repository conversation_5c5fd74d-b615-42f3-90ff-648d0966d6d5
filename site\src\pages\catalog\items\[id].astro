---
import MainLayout from "../../../layouts/MainLayout.astro";
import { trpcClient } from "@/lib/trpc";
import { TrpcProvider } from "@/components/providers/TrpcProvider";
import CatalogItemTemplateIsland from "@/components/templates/islands/CatalogItemTemplateIsland";

const { id } = Astro.params;
if (!id) return Astro.redirect('/catalog');

const itemId = Number(id);
if (isNaN(itemId)) return Astro.redirect('/catalog');

// Загружаем данные для рендеринга шаблона каталожной позиции
let renderData: any = null;
let item: any = null;

try {
  // Получаем данные для рендеринга через специальный роут
  renderData = await trpcClient.pageTemplates.renderCatalogItem.query({ id: itemId });
  item = renderData?.item;

  if (!item) {
    return Astro.redirect('/catalog');
  }

} catch (error) {
  console.error('Error loading catalog item template data:', error);
  return Astro.redirect('/catalog');
}

// Генерируем SEO данные
const title = renderData?.template?.config?.h1
  ? renderData.template.config.h1.replace(/\{\{[^}]+\}\}/g, item.sku)
  : `${item.brand.name} ${item.sku}`;

const description = renderData?.template?.config?.h2
  ? renderData.template.config.h2.replace(/\{\{[^}]+\}\}/g, item.sku)
  : item.description || `Артикул ${item.sku} от ${item.brand.name}`;
---

<MainLayout title={title} description={description}>
  <TrpcProvider client:load>
    <div class="container mx-auto px-4 py-8">
      <!-- Рендеринг через систему шаблонов -->
      <CatalogItemTemplateIsland client:load renderData={renderData} />
    </div>
  </TrpcProvider>
</MainLayout>


