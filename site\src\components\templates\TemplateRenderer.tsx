"use client";

import React from 'react';
import type { 
  TemplateRendererProps, 
  CategoryRenderData, 
  PartRenderData, 
  CatalogItemRenderData 
} from '@/types/templates';
import { CategoryTemplateRenderer } from './CategoryTemplateRenderer';
import { PartTemplateRenderer } from './PartTemplateRenderer';
import { CatalogItemTemplateRenderer } from './CatalogItemTemplateRenderer';
import { FallbackRenderer } from './FallbackRenderer';

/**
 * Базовый компонент для рендеринга шаблонов страниц
 * Автоматически определяет тип шаблона и использует соответствующий рендерер
 */
export function TemplateRenderer({ renderData, fallbackComponent: FallbackComponent }: TemplateRendererProps) {
  // Если нет шаблона, используем fallback рендеринг
  if (!renderData.template) {
    if (FallbackComponent) {
      return <FallbackComponent renderData={renderData} />;
    }
    return <FallbackRenderer renderData={renderData} />;
  }

  // Рендерим в зависимости от типа шаблона
  switch (renderData.template.kind) {
    case 'CATEGORY':
      return (
        <CategoryTemplateRenderer
          config={renderData.template.config}
          data={(renderData as CategoryRenderData).data}
          attr={(renderData as CategoryRenderData).attr}
        />
      );

    case 'PART':
      return (
        <PartTemplateRenderer
          config={renderData.template.config}
          data={(renderData as PartRenderData).data}
          attr={(renderData as PartRenderData).attr}
        />
      );

    case 'CATALOG_ITEM':
      return (
        <CatalogItemTemplateRenderer
          config={renderData.template.config}
          data={(renderData as CatalogItemRenderData).data}
          attr={(renderData as CatalogItemRenderData).attr}
        />
      );

    default:
      console.warn('Неизвестный тип шаблона:', renderData.template.kind);
      if (FallbackComponent) {
        return <FallbackComponent renderData={renderData} />;
      }
      return <FallbackRenderer renderData={renderData} />;
  }
}

/**
 * HOC для обработки ошибок рендеринга шаблонов
 */
export function withTemplateErrorBoundary<T extends object>(
  Component: React.ComponentType<T>
): React.ComponentType<T> {
  return function TemplateErrorBoundary(props: T) {
    return (
      <React.Suspense fallback={<TemplateLoadingFallback />}>
        <ErrorBoundary>
          <Component {...props} />
        </ErrorBoundary>
      </React.Suspense>
    );
  };
}

/**
 * Компонент для отображения состояния загрузки
 */
function TemplateLoadingFallback() {
  return (
    <div className="animate-pulse space-y-4">
      <div className="h-8 bg-gray-200 rounded w-3/4"></div>
      <div className="h-4 bg-gray-200 rounded w-1/2"></div>
      <div className="space-y-2">
        <div className="h-4 bg-gray-200 rounded"></div>
        <div className="h-4 bg-gray-200 rounded w-5/6"></div>
        <div className="h-4 bg-gray-200 rounded w-4/6"></div>
      </div>
    </div>
  );
}

/**
 * Error Boundary для обработки ошибок рендеринга
 */
class ErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Ошибка рендеринга шаблона:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="border border-red-200 bg-red-50 p-4 rounded-lg">
          <h3 className="text-red-800 font-semibold mb-2">Ошибка отображения шаблона</h3>
          <p className="text-red-600 text-sm">
            Произошла ошибка при рендеринге шаблона страницы. 
            Пожалуйста, обратитесь к администратору.
          </p>
          {process.env.NODE_ENV === 'development' && this.state.error && (
            <details className="mt-2">
              <summary className="text-red-700 cursor-pointer">Детали ошибки</summary>
              <pre className="text-xs text-red-600 mt-1 overflow-auto">
                {this.state.error.message}
              </pre>
            </details>
          )}
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * Хук для использования данных рендеринга шаблона
 */
export function useTemplateRenderer(renderData: any) {
  const hasTemplate = Boolean(renderData?.template);
  const templateKind = renderData?.template?.kind;
  const templateConfig = renderData?.template?.config;

  return {
    hasTemplate,
    templateKind,
    templateConfig,
    canRender: hasTemplate && templateConfig,
    shouldUseFallback: !hasTemplate || !templateConfig
  };
}
