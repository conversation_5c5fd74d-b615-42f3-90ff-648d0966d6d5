"use client";

import React from 'react';
import { Template<PERSON>enderer } from '../TemplateRenderer';
import type { CatalogItemRenderData } from '@/types/templates';

interface CatalogItemTemplateIslandProps {
  renderData: CatalogItemRenderData;
}

/**
 * Island компонент для рендеринга страниц каталожных позиций с шаблонами
 */
export default function CatalogItemTemplateIsland({ renderData }: CatalogItemTemplateIslandProps) {
  return <TemplateRenderer renderData={renderData} />;
}
