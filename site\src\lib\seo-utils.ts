import type { 
  PageSEOData, 
  CategoryRenderData, 
  PartRenderData, 
  CatalogItemRenderData 
} from '@/types/templates';
import { interpolate, createInterpolationContext } from './template-utils';

/**
 * Генерирует SEO данные для страницы категории
 */
export function generateCategorySEO(renderData: CategoryRenderData): PageSEOData {
  const { template, category, data, attr } = renderData;
  const context = createInterpolationContext(data, attr);

  // Базовые значения
  let title = category.name;
  let description = category.description || `Запчасти категории ${category.name}`;
  
  // Если есть шаблон, используем его
  if (template?.config) {
    if (template.config.h1) {
      title = interpolate(template.config.h1, context);
    }
    if (template.config.description) {
      description = interpolate(template.config.description, context);
    }
  }

  // Структурированные данные для категории
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    "name": title,
    "description": description,
    "url": `${getBaseUrl()}/catalog/categories/${category.slug}`,
    "breadcrumb": {
      "@type": "BreadcrumbList",
      "itemListElement": generateCategoryBreadcrumbs(category)
    },
    "mainEntity": {
      "@type": "ItemList",
      "numberOfItems": category._count.parts,
      "name": `Запчасти категории ${category.name}`
    }
  };

  // Open Graph данные
  const openGraph = {
    title,
    description,
    type: "website",
    image: category.image?.url
  };

  return {
    title,
    description,
    keywords: generateCategoryKeywords(category),
    structuredData,
    openGraph
  };
}

/**
 * Генерирует SEO данные для страницы запчасти
 */
export function generatePartSEO(renderData: PartRenderData): PageSEOData {
  const { template, part, data, attr } = renderData;
  const context = createInterpolationContext(data, attr);

  // Базовые значения
  let title = part.name || `Запчасть #${part.id}`;
  let description = `Группа взаимозаменяемости: ${part.name || `Запчасть #${part.id}`}`;
  
  // Если есть шаблон, используем его
  if (template?.config) {
    if (template.config.h1) {
      title = interpolate(template.config.h1, context);
    }
    if (template.config.h2) {
      description = interpolate(template.config.h2, context);
    }
  }

  // Структурированные данные для запчасти
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Product",
    "name": title,
    "description": description,
    "category": part.partCategory.name,
    "identifier": part.id.toString(),
    "url": `${getBaseUrl()}/catalog/parts/${part.id}`,
    "image": part.image?.url,
    "additionalProperty": part.attributes.map(attr => ({
      "@type": "PropertyValue",
      "name": attr.template.title,
      "value": attr.value,
      "unitText": attr.template.unit
    }))
  };

  // Open Graph данные
  const openGraph = {
    title,
    description,
    type: "product",
    image: part.image?.url
  };

  return {
    title,
    description,
    keywords: generatePartKeywords(part),
    structuredData,
    openGraph
  };
}

/**
 * Генерирует SEO данные для страницы каталожной позиции
 */
export function generateCatalogItemSEO(renderData: CatalogItemRenderData): PageSEOData {
  const { template, item, data, attr } = renderData;
  const context = createInterpolationContext(data, attr);

  // Базовые значения
  let title = `${item.brand.name} ${item.sku}`;
  let description = item.description || `Артикул ${item.sku} от ${item.brand.name}`;
  
  // Если есть шаблон, используем его
  if (template?.config) {
    if (template.config.h1) {
      title = interpolate(template.config.h1, context);
    }
    if (template.config.h2) {
      description = interpolate(template.config.h2, context);
    }
  }

  // Структурированные данные для каталожной позиции
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Product",
    "name": title,
    "description": description,
    "sku": item.sku,
    "brand": {
      "@type": "Brand",
      "name": item.brand.name
    },
    "manufacturer": item.brand.isOem ? {
      "@type": "Organization",
      "name": item.brand.name
    } : undefined,
    "identifier": item.id.toString(),
    "url": `${getBaseUrl()}/catalog/items/${item.id}`,
    "image": item.image?.url,
    "additionalProperty": item.attributes.map(attr => ({
      "@type": "PropertyValue",
      "name": attr.template.title,
      "value": attr.value,
      "unitText": attr.template.unit
    }))
  };

  // Open Graph данные
  const openGraph = {
    title,
    description,
    type: "product",
    image: item.image?.url
  };

  return {
    title,
    description,
    keywords: generateCatalogItemKeywords(item),
    structuredData,
    openGraph
  };
}

/**
 * Генерирует хлебные крошки для категории
 */
function generateCategoryBreadcrumbs(category: any): any[] {
  const breadcrumbs = [
    {
      "@type": "ListItem",
      "position": 1,
      "name": "Главная",
      "item": getBaseUrl()
    },
    {
      "@type": "ListItem",
      "position": 2,
      "name": "Категории",
      "item": `${getBaseUrl()}/categories`
    }
  ];

  if (category.parent) {
    breadcrumbs.push({
      "@type": "ListItem",
      "position": 3,
      "name": category.parent.name,
      "item": `${getBaseUrl()}/catalog/categories/${category.parent.slug}`
    });
  }

  breadcrumbs.push({
    "@type": "ListItem",
    "position": breadcrumbs.length + 1,
    "name": category.name,
    "item": `${getBaseUrl()}/catalog/categories/${category.slug}`
  });

  return breadcrumbs;
}

/**
 * Генерирует ключевые слова для категории
 */
function generateCategoryKeywords(category: any): string[] {
  const keywords = [
    category.name,
    'запчасти',
    'каталог',
    'взаимозаменяемость'
  ];

  if (category.parent) {
    keywords.push(category.parent.name);
  }

  return keywords;
}

/**
 * Генерирует ключевые слова для запчасти
 */
function generatePartKeywords(part: any): string[] {
  const keywords = [
    part.name || `запчасть ${part.id}`,
    part.partCategory.name,
    'запчасти',
    'аналоги',
    'взаимозаменяемость'
  ];

  // Добавляем значения атрибутов как ключевые слова
  part.attributes.forEach((attr: any) => {
    if (attr.value && attr.value !== '—') {
      keywords.push(attr.value);
    }
  });

  return keywords;
}

/**
 * Генерирует ключевые слова для каталожной позиции
 */
function generateCatalogItemKeywords(item: any): string[] {
  const keywords = [
    item.sku,
    item.brand.name,
    'артикул',
    'запчасти'
  ];

  if (item.brand.isOem) {
    keywords.push('OEM', 'оригинал');
  } else {
    keywords.push('aftermarket', 'аналог');
  }

  // Добавляем значения атрибутов как ключевые слова
  item.attributes.forEach((attr: any) => {
    if (attr.value && attr.value !== '—') {
      keywords.push(attr.value);
    }
  });

  return keywords;
}

/**
 * Получает базовый URL сайта
 */
function getBaseUrl(): string {
  if (typeof window !== 'undefined') {
    return window.location.origin;
  }
  return process.env.PUBLIC_SITE_URL || 'https://parttec.ru';
}

/**
 * Генерирует JSON-LD скрипт для структурированных данных
 */
export function generateStructuredDataScript(structuredData: Record<string, any>): string {
  return `<script type="application/ld+json">${JSON.stringify(structuredData, null, 2)}</script>`;
}
