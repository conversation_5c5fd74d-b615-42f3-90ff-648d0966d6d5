"use client";

import React from 'react';
import type { CategoryTemplateRendererProps } from '@/types/templates';
import { 
  interpolate, 
  createInterpolationContext, 
  renderAttributesList,
  createFallbackContent 
} from '@/lib/template-utils';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';

/**
 * Компонент для рендеринга страниц категорий на основе шаблонов
 */
export function CategoryTemplateRenderer({ config, data, attr }: CategoryTemplateRendererProps) {
  const context = createInterpolationContext(data, attr);
  const category = data.category;

  // Интерполируем заголовки и описание
  const h1 = config.h1 
    ? interpolate(config.h1, context)
    : createFallbackContent('h1', category.name, 'category');

  const h2 = config.h2 
    ? interpolate(config.h2, context)
    : createFallbackContent('h2', category.name, 'category');

  const description = config.description 
    ? interpolate(config.description, context)
    : createFallbackContent('description', category.name, 'category');

  const footer = config.footer 
    ? interpolate(config.footer, context)
    : null;

  // Рендерим фильтры если они настроены
  const filters = config.filters 
    ? renderAttributesList(config.filters, attr)
    : [];

  // Рендерим атрибуты товаров если они настроены
  const productAttrs = config.productAttrs 
    ? renderAttributesList(config.productAttrs, attr)
    : [];

  return (
    <div className="space-y-6">
      {/* Основной заголовок */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">{h1}</h1>
        {h2 && h2 !== h1 && (
          <h2 className="text-xl text-muted-foreground">{h2}</h2>
        )}
      </div>

      {/* Изображение категории */}
      {category.image && (
        <div className="relative w-full h-48 md:h-64 rounded-lg overflow-hidden">
          <img
            src={category.image.url}
            alt={category.image.alt || category.name}
            className="w-full h-full object-cover"
          />
        </div>
      )}

      {/* Описание */}
      {description && (
        <div className="prose prose-gray max-w-none">
          <p className="text-lg leading-relaxed">{description}</p>
        </div>
      )}

      {/* Информация о категории */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {category.icon && (
              <span className="text-2xl">{category.icon}</span>
            )}
            Информация о категории
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <span className="text-sm font-medium text-muted-foreground">Количество запчастей:</span>
              <p className="text-lg font-semibold">{category._count.parts}</p>
            </div>
            <div>
              <span className="text-sm font-medium text-muted-foreground">Уровень вложенности:</span>
              <p className="text-lg font-semibold">{category.level}</p>
            </div>
          </div>

          {/* Путь в иерархии */}
          <div>
            <span className="text-sm font-medium text-muted-foreground">Путь:</span>
            <p className="text-sm text-muted-foreground mt-1">{category.path}</p>
          </div>

          {/* Родительская категория */}
          {category.parent && (
            <div>
              <span className="text-sm font-medium text-muted-foreground">Родительская категория:</span>
              <Badge variant="outline" className="ml-2">
                {category.parent.name}
              </Badge>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Фильтры */}
      {filters.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Доступные фильтры</CardTitle>
            <CardDescription>
              Атрибуты для фильтрации запчастей в этой категории
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filters.map((filter) => (
                <div key={filter.name} className="space-y-1">
                  <span className="text-sm font-medium">{filter.title}</span>
                  <p className="text-sm text-muted-foreground">{filter.value}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Подкатегории */}
      {category.children && category.children.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Подкатегории</CardTitle>
            <CardDescription>
              Дочерние категории в {category.name}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {category.children.map((child) => (
                <Card key={child.id} className="hover:shadow-md transition-shadow cursor-pointer">
                  <CardContent className="p-4">
                    <h3 className="font-semibold">{child.name}</h3>
                    <p className="text-sm text-muted-foreground mt-1">
                      {child._count.parts} запчастей
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Атрибуты товаров */}
      {productAttrs.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Характеристики товаров</CardTitle>
            <CardDescription>
              Основные атрибуты запчастей в этой категории
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {productAttrs.map((attr) => (
                <div key={attr.name} className="flex justify-between items-center py-2">
                  <span className="text-sm font-medium">{attr.title}</span>
                  <span className="text-sm text-muted-foreground">{attr.value}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Футер */}
      {footer && (
        <>
          <Separator />
          <div className="prose prose-gray max-w-none">
            <div dangerouslySetInnerHTML={{ __html: footer }} />
          </div>
        </>
      )}
    </div>
  );
}

/**
 * Компонент страницы категории с поддержкой шаблонов
 */
export function CategoryPageRenderer({ renderData }: { renderData: any }) {
  if (!renderData) {
    return (
      <div className="text-center py-12">
        <h1 className="text-2xl font-bold text-muted-foreground">Категория не найдена</h1>
        <p className="text-muted-foreground mt-2">
          Запрашиваемая категория не существует или была удалена.
        </p>
      </div>
    );
  }

  // Если есть шаблон, используем его
  if (renderData.template && renderData.template.config) {
    return (
      <CategoryTemplateRenderer
        config={renderData.template.config}
        data={renderData.data}
        attr={renderData.attr}
      />
    );
  }

  // Fallback рендеринг без шаблона
  const category = renderData.category;
  
  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">{category.name}</h1>
        {category.description && (
          <p className="text-lg text-muted-foreground">{category.description}</p>
        )}
      </div>

      {category.image && (
        <div className="relative w-full h-48 md:h-64 rounded-lg overflow-hidden">
          <img
            src={category.image.url}
            alt={category.image.alt || category.name}
            className="w-full h-full object-cover"
          />
        </div>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Информация о категории</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <span className="text-sm font-medium text-muted-foreground">Количество запчастей:</span>
              <p className="text-lg font-semibold">{category._count.parts}</p>
            </div>
            <div>
              <span className="text-sm font-medium text-muted-foreground">Уровень:</span>
              <p className="text-lg font-semibold">{category.level}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
