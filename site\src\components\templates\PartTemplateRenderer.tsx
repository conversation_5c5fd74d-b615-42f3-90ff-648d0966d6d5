"use client";

import React from 'react';
import type { PartTemplateRendererProps } from '@/types/templates';
import { 
  interpolate, 
  createInterpolationContext, 
  renderAttributesList,
  createFallbackContent 
} from '@/lib/template-utils';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { navigate } from 'astro:transitions/client';

/**
 * Компонент для рендеринга страниц запчастей на основе шаблонов
 */
export function PartTemplateRenderer({ config, data, attr }: PartTemplateRendererProps) {
  const context = createInterpolationContext(data, attr);
  const part = data.part;

  // Интерполируем заголовки
  const h1 = config.h1 
    ? interpolate(config.h1, context)
    : createFallbackContent('h1', part.name || `Запчасть #${part.id}`, 'part');

  const h2 = config.h2 
    ? interpolate(config.h2, context)
    : createFallbackContent('h2', part.name || `Запчасть #${part.id}`, 'part');

  // Рендерим атрибуты согласно конфигурации
  const attributes = renderAttributesList(config.attributes, attr);

  const handleCategoryClick = () => {
    navigate(`/catalog/categories/${part.partCategory.slug}`);
  };

  return (
    <div className="space-y-6">
      {/* Основной заголовок */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">{h1}</h1>
        {h2 && h2 !== h1 && (
          <h2 className="text-xl text-muted-foreground">{h2}</h2>
        )}
      </div>

      {/* Навигация */}
      <div className="flex items-center gap-2 text-sm text-muted-foreground">
        <Button 
          variant="link" 
          className="p-0 h-auto text-sm"
          onClick={handleCategoryClick}
        >
          {part.partCategory.name}
        </Button>
        <span>→</span>
        <span>{part.name || `Запчасть #${part.id}`}</span>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Основная информация */}
        <div className="lg:col-span-2 space-y-6">
          {/* Изображение */}
          {part.image && (
            <Card>
              <CardContent className="p-6">
                <div className="relative w-full h-64 md:h-80 rounded-lg overflow-hidden">
                  <img
                    src={part.image.url}
                    alt={part.image.alt || part.name || 'Изображение запчасти'}
                    className="w-full h-full object-cover"
                  />
                </div>
              </CardContent>
            </Card>
          )}

          {/* Характеристики */}
          {attributes.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Технические характеристики</CardTitle>
                <CardDescription>
                  Основные параметры и свойства запчасти
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {attributes.map((attr) => (
                    <div key={attr.name} className="flex justify-between items-center py-2 border-b border-border/50 last:border-b-0">
                      <span className="text-sm font-medium">{attr.title}</span>
                      <span className="text-sm text-muted-foreground font-mono">{attr.value}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Дополнительные медиафайлы */}
          {part.mediaAssets && part.mediaAssets.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Дополнительные материалы</CardTitle>
                <CardDescription>
                  Схемы, чертежи и другие документы
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {part.mediaAssets.map((media) => (
                    <div key={media.id} className="relative group">
                      {media.type.startsWith('image/') ? (
                        <img
                          src={media.url}
                          alt={media.alt || 'Дополнительное изображение'}
                          className="w-full h-24 object-cover rounded-lg border"
                        />
                      ) : (
                        <div className="w-full h-24 bg-muted rounded-lg border flex items-center justify-center">
                          <span className="text-xs text-muted-foreground">
                            {media.type.split('/')[1]?.toUpperCase() || 'FILE'}
                          </span>
                        </div>
                      )}
                      <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                        <Button size="sm" variant="secondary">
                          Открыть
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Боковая панель */}
        <div className="space-y-6">
          {/* Информация о запчасти */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Информация</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <span className="text-sm font-medium text-muted-foreground">Категория:</span>
                <Badge 
                  variant="outline" 
                  className="ml-2 cursor-pointer hover:bg-accent"
                  onClick={handleCategoryClick}
                >
                  {part.partCategory.name}
                </Badge>
              </div>

              <div>
                <span className="text-sm font-medium text-muted-foreground">Уровень:</span>
                <p className="text-sm mt-1">{part.level}</p>
              </div>

              <div>
                <span className="text-sm font-medium text-muted-foreground">Путь в иерархии:</span>
                <p className="text-xs text-muted-foreground mt-1 font-mono">{part.path}</p>
              </div>

              <div>
                <span className="text-sm font-medium text-muted-foreground">ID запчасти:</span>
                <p className="text-sm mt-1 font-mono">#{part.id}</p>
              </div>
            </CardContent>
          </Card>

          {/* Действия */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Действия</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button className="w-full" variant="default">
                Найти аналоги
              </Button>
              <Button className="w-full" variant="outline">
                Показать применимость
              </Button>
              <Button className="w-full" variant="outline">
                Экспорт данных
              </Button>
            </CardContent>
          </Card>

          {/* Статистика */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Статистика</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Атрибутов:</span>
                <span className="text-sm font-semibold">{part.attributes.length}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Медиафайлов:</span>
                <span className="text-sm font-semibold">{part.mediaAssets?.length || 0}</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

/**
 * Компонент страницы запчасти с поддержкой шаблонов
 */
export function PartPageRenderer({ renderData }: { renderData: any }) {
  if (!renderData) {
    return (
      <div className="text-center py-12">
        <h1 className="text-2xl font-bold text-muted-foreground">Запчасть не найдена</h1>
        <p className="text-muted-foreground mt-2">
          Запрашиваемая запчасть не существует или была удалена.
        </p>
      </div>
    );
  }

  // Если есть шаблон, используем его
  if (renderData.template && renderData.template.config) {
    return (
      <PartTemplateRenderer
        config={renderData.template.config}
        data={renderData.data}
        attr={renderData.attr}
      />
    );
  }

  // Fallback рендеринг без шаблона
  const part = renderData.part;
  
  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">
          {part.name || `Запчасть #${part.id}`}
        </h1>
        <Badge variant="outline">{part.partCategory.name}</Badge>
      </div>

      {part.image && (
        <div className="relative w-full h-48 md:h-64 rounded-lg overflow-hidden">
          <img
            src={part.image.url}
            alt={part.image.alt || part.name || 'Изображение запчасти'}
            className="w-full h-full object-cover"
          />
        </div>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Информация о запчасти</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <span className="text-sm font-medium text-muted-foreground">Категория:</span>
              <p className="text-lg font-semibold">{part.partCategory.name}</p>
            </div>
            <div>
              <span className="text-sm font-medium text-muted-foreground">Уровень:</span>
              <p className="text-lg font-semibold">{part.level}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
