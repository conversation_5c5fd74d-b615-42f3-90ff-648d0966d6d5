import { z } from 'zod';

// Общий тип шаблонной строки: поддерживаем простую интерполяцию вида {{attr.name}}
export const TemplateStringSchema = z.string().max(5000);

export const AttributeListConfigSchema = z.object({
  attributeNames: z.array(z.string().min(1)).default([]),
  sortOrder: z.array(z.string().min(1)).default([]),
  withUnits: z.boolean().default(false),
});

export const CategoryTemplateConfigSchema = z.object({
  h1: TemplateStringSchema.optional(),
  h2: TemplateStringSchema.optional(),
  description: TemplateStringSchema.optional(),
  footer: TemplateStringSchema.optional(),
  filters: AttributeListConfigSchema.optional(),
  productAttrs: AttributeListConfigSchema.optional(),
});

export const PartTemplateConfigSchema = z.object({
  h1: TemplateStringSchema.optional(),
  h2: TemplateStringSchema.optional(),
  attributes: AttributeListConfigSchema,
});

export const CatalogItemTemplateConfigSchema = z.object({
  h1: TemplateStringSchema.optional(),
  h2: TemplateStringSchema.optional(),
  attributes: AttributeListConfigSchema,
});

export const TemplateKindSchema = z.enum(['CATEGORY', 'PART', 'CATALOG_ITEM']);

const PageTemplateBaseSchema = z.object({
  name: z.string().min(1).max(150),
  description: z.string().max(1000).optional(),
  kind: TemplateKindSchema,
  partCategoryId: z.number().int().positive().optional(),
  isActive: z.boolean().default(true),
  isDefault: z.boolean().default(false),
  categoryConfig: CategoryTemplateConfigSchema.optional(),
  partConfig: PartTemplateConfigSchema.optional(),
  catalogItemConfig: CatalogItemTemplateConfigSchema.optional(),
});

export const PageTemplateCreateSchema = PageTemplateBaseSchema.superRefine((val, ctx) => {
  if (val.kind === 'CATEGORY' && !val.categoryConfig) {
    ctx.addIssue({ code: z.ZodIssueCode.custom, message: 'categoryConfig обязателен для kind=CATEGORY' });
  }
  if (val.kind === 'PART' && !val.partConfig) {
    ctx.addIssue({ code: z.ZodIssueCode.custom, message: 'partConfig обязателен для kind=PART' });
  }
  if (val.kind === 'CATALOG_ITEM' && !val.catalogItemConfig) {
    ctx.addIssue({ code: z.ZodIssueCode.custom, message: 'catalogItemConfig обязателен для kind=CATALOG_ITEM' });
  }
});

export const PageTemplateUpdateSchema = PageTemplateBaseSchema.partial().extend({ id: z.string().uuid() });

export type AttributeListConfig = z.infer<typeof AttributeListConfigSchema>;
export type CategoryTemplateConfig = z.infer<typeof CategoryTemplateConfigSchema>;
export type PartTemplateConfig = z.infer<typeof PartTemplateConfigSchema>;
export type CatalogItemTemplateConfig = z.infer<typeof CatalogItemTemplateConfigSchema>;
export type TemplateKind = z.infer<typeof TemplateKindSchema>;

