"use client";

import React from 'react';
import { Template<PERSON>enderer } from '../TemplateRenderer';
import type { CategoryRenderData } from '@/types/templates';

interface CategoryTemplateIslandProps {
  renderData: CategoryRenderData;
}

/**
 * Island компонент для рендеринга страниц категорий с шаблонами
 */
export default function CategoryTemplateIsland({ renderData }: CategoryTemplateIslandProps) {
  return <TemplateRenderer renderData={renderData} />;
}
