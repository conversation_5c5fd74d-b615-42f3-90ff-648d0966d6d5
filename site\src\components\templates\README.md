# Система шаблонов страниц

Система шаблонов позволяет динамически настраивать отображение страниц категорий, запчастей и каталожных позиций через админ-панель.

## Архитектура

### Основные компоненты

- **TemplateRenderer** - базовый компонент для рендеринга шаблонов
- **CategoryTemplateRenderer** - рендеринг страниц категорий
- **PartTemplateRenderer** - рендеринг страниц запчастей  
- **CatalogItemTemplateRenderer** - рендеринг страниц каталожных позиций
- **FallbackRenderer** - fallback рендеринг при отсутствии шаблонов

### Типы шаблонов

1. **CATEGORY** - шаблоны для страниц категорий
2. **PART** - шаблоны для страниц групп запчастей
3. **CATALOG_ITEM** - шаблоны для страниц артикулов

## Использование

### В Astro страницах

```astro
---
import { trpcClient } from "@/lib/trpc";
import CategoryTemplateIsland from "@/components/templates/islands/CategoryTemplateIsland";

// Загружаем данные для рендеринга
const renderData = await trpcClient.pageTemplates.renderCategory.query({ slug });
---

<CategoryTemplateIsland client:load renderData={renderData} />
```

### В React компонентах

```tsx
import { TemplateRenderer } from '@/components/templates';

function MyPage({ renderData }) {
  return <TemplateRenderer renderData={renderData} />;
}
```

## Интерполяция переменных

Система поддерживает подстановку переменных в шаблоны:

### Доступные переменные

#### Для категорий
- `{{data.category.name}}` - название категории
- `{{data.category.description}}` - описание категории
- `{{attr.ATTRIBUTE_NAME}}` - значение атрибута

#### Для запчастей
- `{{data.part.name}}` - название запчасти
- `{{data.part.partCategory.name}}` - название категории
- `{{attr.ATTRIBUTE_NAME}}` - значение атрибута

#### Для каталожных позиций
- `{{data.item.sku}}` - артикул
- `{{data.brand.name}}` - название бренда
- `{{attr.ATTRIBUTE_NAME}}` - значение атрибута

### Примеры шаблонов

```html
<!-- Заголовок H1 для категории -->
Запчасти {{data.category.name}} - каталог PartTec

<!-- Описание для запчасти -->
{{data.part.name}} - группа взаимозаменяемости с диаметром {{attr.diameter}}

<!-- Заголовок для артикула -->
{{data.brand.name}} {{data.item.sku}} - оригинальная запчасть
```

## Конфигурация шаблонов

### Шаблон категории

```typescript
interface CategoryTemplateConfig {
  h1?: string;           // Заголовок H1
  h2?: string;           // Заголовок H2  
  description?: string;  // Описание
  footer?: string;       // Футер
  filters?: AttributeListConfig;      // Фильтры
  productAttrs?: AttributeListConfig; // Атрибуты товаров
}
```

### Шаблон запчасти

```typescript
interface PartTemplateConfig {
  h1?: string;                    // Заголовок H1
  h2?: string;                    // Заголовок H2
  attributes: AttributeListConfig; // Список атрибутов
}
```

### Шаблон каталожной позиции

```typescript
interface CatalogItemTemplateConfig {
  h1?: string;                    // Заголовок H1
  h2?: string;                    // Заголовок H2
  attributes: AttributeListConfig; // Список атрибутов
}
```

### Конфигурация атрибутов

```typescript
interface AttributeListConfig {
  attributeNames: string[];  // Имена атрибутов для отображения
  sortOrder: string[];      // Порядок сортировки
  withUnits: boolean;       // Показывать единицы измерения
}
```

## SEO оптимизация

Система автоматически генерирует:

- **Meta-теги** - title, description, keywords
- **Open Graph** - для социальных сетей
- **Структурированные данные** - JSON-LD для поисковых систем
- **Хлебные крошки** - для навигации

### Пример использования SEO

```astro
---
import { generateCategorySEO, generateStructuredDataScript } from "@/lib/seo-utils";

const seoData = generateCategorySEO(renderData);
const structuredDataScript = generateStructuredDataScript(seoData.structuredData);
---

<MainLayout title={seoData.title} description={seoData.description}>
  <Fragment slot="head">
    <meta name="keywords" content={seoData.keywords?.join(', ')} />
    <Fragment set:html={structuredDataScript} />
  </Fragment>
  <!-- Контент страницы -->
</MainLayout>
```

## Fallback рендеринг

При отсутствии шаблона или ошибке система автоматически использует fallback рендеринг:

- Показывает предупреждение о режиме по умолчанию
- Отображает базовую информацию о сущности
- Обеспечивает корректную работу сайта

## Обработка ошибок

Система включает:

- **Error Boundary** - для перехвата ошибок рендеринга
- **Loading состояния** - для показа загрузки
- **Валидация шаблонов** - проверка корректности синтаксиса
- **Логирование ошибок** - для отладки

## API роуты

### Получение данных для рендеринга

```typescript
// Категория
trpcClient.pageTemplates.renderCategory.query({ slug: "engine" })

// Запчасть  
trpcClient.pageTemplates.renderPart.query({ id: 123 })

// Каталожная позиция
trpcClient.pageTemplates.renderCatalogItem.query({ id: 456 })
```

### Управление шаблонами

```typescript
// Список шаблонов
trpcClient.pageTemplates.list.query({ kind: "CATEGORY" })

// Получение шаблона
trpcClient.pageTemplates.byId.query({ id: "uuid" })

// Создание шаблона (требует авторизации)
trpcClient.pageTemplates.create.mutate(templateData)
```

## Лучшие практики

1. **Используйте осмысленные переменные** - `{{data.category.name}}` вместо жестко заданного текста
2. **Добавляйте fallback значения** - система автоматически создаст базовый контент
3. **Тестируйте шаблоны** - проверяйте корректность интерполяции
4. **Оптимизируйте SEO** - используйте переменные в meta-тегах
5. **Следите за производительностью** - избегайте сложных вычислений в шаблонах

## Расширение системы

Для добавления нового типа шаблона:

1. Добавьте тип в `TemplateKind`
2. Создайте конфигурацию в типах
3. Реализуйте компонент рендеринга
4. Добавьте обработку в `TemplateRenderer`
5. Создайте API роуты для получения данных

## Отладка

Для отладки шаблонов используйте:

```typescript
import { validateTemplate, extractTemplateVariables } from '@/lib/template-utils';

// Проверка корректности шаблона
const validation = validateTemplate("{{data.category.name}}");

// Извлечение переменных
const variables = extractTemplateVariables("{{data.category.name}} - {{attr.diameter}}");
```
