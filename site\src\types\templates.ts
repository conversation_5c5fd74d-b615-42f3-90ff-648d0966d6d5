// Типы для системы шаблонов страниц в клиентской части

// Базовые типы из API
export type TemplateKind = 'CATEGORY' | 'PART' | 'CATALOG_ITEM';

// Конфигурация списка атрибутов
export interface AttributeListConfig {
  attributeNames: string[];
  sortOrder: string[];
  withUnits: boolean;
}

// Конфигурация шаблона категории
export interface CategoryTemplateConfig {
  h1?: string;
  h2?: string;
  description?: string;
  footer?: string;
  filters?: AttributeListConfig;
  productAttrs?: AttributeListConfig;
}

// Конфигурация шаблона запчасти
export interface PartTemplateConfig {
  h1?: string;
  h2?: string;
  attributes: AttributeListConfig;
}

// Конфигурация шаблона каталожной позиции
export interface CatalogItemTemplateConfig {
  h1?: string;
  h2?: string;
  attributes: AttributeListConfig;
}

// Основная модель шаблона страницы
export interface PageTemplate {
  id: string;
  name: string;
  description?: string | null;
  kind: TemplateKind;
  partCategoryId?: number | null;
  isActive: boolean;
  isDefault: boolean;
  categoryConfig?: CategoryTemplateConfig | null;
  partConfig?: PartTemplateConfig | null;
  catalogItemConfig?: CatalogItemTemplateConfig | null;
  createdAt: Date;
  updatedAt: Date;
}

// Данные атрибута для интерполяции
export interface AttributeData {
  value: string;
  unit?: string | null;
  template?: {
    name: string;
    title: string;
    unit?: string | null;
  };
}

// Словарь атрибутов для интерполяции (ключ - имя атрибута)
export type AttributeDict = Record<string, AttributeData>;

// Данные категории для рендеринга
export interface CategoryRenderData {
  template: {
    id: string;
    kind: TemplateKind;
    config: CategoryTemplateConfig;
  } | null;
  category: {
    id: number;
    name: string;
    slug: string;
    description?: string | null;
    level: number;
    path: string;
    icon?: string | null;
    image?: {
      id: number;
      url: string;
      alt?: string | null;
    } | null;
    parent?: {
      id: number;
      name: string;
      slug: string;
    } | null;
    children?: Array<{
      id: number;
      name: string;
      slug: string;
      _count: { parts: number };
    }>;
    _count: { parts: number };
  };
  data: {
    category: any;
  };
  attr: AttributeDict;
}

// Данные запчасти для рендеринга
export interface PartRenderData {
  template: {
    id: string;
    kind: TemplateKind;
    config: PartTemplateConfig;
  } | null;
  part: {
    id: number;
    name?: string | null;
    level: number;
    path: string;
    partCategory: {
      id: number;
      name: string;
      slug: string;
    };
    image?: {
      id: number;
      url: string;
      alt?: string | null;
    } | null;
    mediaAssets?: Array<{
      id: number;
      url: string;
      alt?: string | null;
      type: string;
    }>;
    attributes: Array<{
      id: number;
      value: string;
      numericValue?: number | null;
      template: {
        id: number;
        name: string;
        title: string;
        unit?: string | null;
      };
    }>;
  };
  data: {
    part: any;
  };
  attr: AttributeDict;
}

// Данные каталожной позиции для рендеринга
export interface CatalogItemRenderData {
  template: {
    id: string;
    kind: TemplateKind;
    config: CatalogItemTemplateConfig;
  } | null;
  item: {
    id: number;
    sku: string;
    source?: string | null;
    description?: string | null;
    brand: {
      id: number;
      name: string;
      isOem: boolean;
    };
    image?: {
      id: number;
      url: string;
      alt?: string | null;
    } | null;
    mediaAssets?: Array<{
      id: number;
      url: string;
      alt?: string | null;
      type: string;
    }>;
    attributes: Array<{
      id: number;
      value: string;
      numericValue?: number | null;
      template: {
        id: number;
        name: string;
        title: string;
        unit?: string | null;
      };
    }>;
  };
  data: {
    item: any;
    brand: any;
  };
  attr: AttributeDict;
}

// Общий тип для данных рендеринга
export type RenderData = CategoryRenderData | PartRenderData | CatalogItemRenderData;

// Пропсы для компонентов рендеринга
export interface TemplateRendererProps {
  renderData: RenderData;
  fallbackComponent?: React.ComponentType<any>;
}

export interface CategoryTemplateRendererProps {
  config: CategoryTemplateConfig;
  data: CategoryRenderData['data'];
  attr: AttributeDict;
}

export interface PartTemplateRendererProps {
  config: PartTemplateConfig;
  data: PartRenderData['data'];
  attr: AttributeDict;
}

export interface CatalogItemTemplateRendererProps {
  config: CatalogItemTemplateConfig;
  data: CatalogItemRenderData['data'];
  attr: AttributeDict;
}

// Результат интерполяции
export interface InterpolationResult {
  content: string;
  hasErrors: boolean;
  errors: string[];
}

// Контекст для интерполяции
export interface InterpolationContext {
  data: Record<string, any>;
  attr: AttributeDict;
}

// Опции для рендеринга атрибутов
export interface AttributeRenderOptions {
  withUnits?: boolean;
  sortOrder?: string[];
  className?: string;
  showEmpty?: boolean;
}

// SEO данные для страницы
export interface PageSEOData {
  title: string;
  description?: string;
  keywords?: string[];
  structuredData?: Record<string, any>;
  openGraph?: {
    title?: string;
    description?: string;
    image?: string;
    type?: string;
  };
}
