import type { 
  AttributeDict, 
  InterpolationResult, 
  InterpolationContext,
  AttributeRenderOptions,
  AttributeListConfig
} from '@/types/templates';

/**
 * Интерполирует шаблонную строку с данными
 * Поддерживает переменные вида {{category.name}}, {{attr.diameter}}, {{brand.name}}
 */
export function interpolateTemplate(
  template: string, 
  context: InterpolationContext
): InterpolationResult {
  if (!template) {
    return { content: '', hasErrors: false, errors: [] };
  }

  const errors: string[] = [];
  let hasErrors = false;

  const content = template.replace(/\{\{\s*([^}]+)\s*\}\}/g, (match, expr) => {
    try {
      const path = expr.trim().split('.');
      let current: any = context;

      for (const key of path) {
        current = current?.[key];
      }

      // Специальный случай: если конечный объект имеет поле value,
      // возвращаем его значение по умолчанию (для attr.NAME)
      if (current && typeof current === 'object' && 'value' in current) {
        return current.value ?? '';
      }

      return current ?? '';
    } catch (error) {
      hasErrors = true;
      errors.push(`Ошибка интерполяции: ${expr} - ${error}`);
      return match; // Возвращаем исходную строку при ошибке
    }
  });

  return { content, hasErrors, errors };
}

/**
 * Безопасная интерполяция - возвращает только контент
 */
export function interpolate(template: string, context: InterpolationContext): string {
  const result = interpolateTemplate(template, context);
  return result.content;
}

/**
 * Получает значение атрибута по имени
 */
export function getAttributeValue(
  attributeName: string, 
  attr: AttributeDict, 
  withUnit = false
): string {
  const attrData = attr[attributeName];
  if (!attrData) {
    return '—';
  }

  const value = attrData.value || '—';

  if (withUnit && attrData.unit) {
    return `${value} ${attrData.unit}`;
  }

  return value;
}

/**
 * Рендерит список атрибутов согласно конфигурации
 */
export function renderAttributesList(
  config: AttributeListConfig,
  attr: AttributeDict,
  options: AttributeRenderOptions = {}
): Array<{ name: string; title: string; value: string; unit?: string }> {
  const { withUnits = config.withUnits, sortOrder = config.sortOrder, showEmpty = false } = options;
  
  let attributeNames = [...config.attributeNames];

  // Применяем пользовательский порядок сортировки
  if (sortOrder.length > 0) {
    attributeNames.sort((a, b) => {
      const indexA = sortOrder.indexOf(a);
      const indexB = sortOrder.indexOf(b);
      
      // Если оба элемента есть в sortOrder
      if (indexA !== -1 && indexB !== -1) {
        return indexA - indexB;
      }
      
      // Если только один элемент есть в sortOrder
      if (indexA !== -1) return -1;
      if (indexB !== -1) return 1;
      
      // Если ни одного элемента нет в sortOrder, сортируем по алфавиту
      return a.localeCompare(b);
    });
  }

  return attributeNames
    .map(name => {
      const attrData = attr[name];
      if (!attrData && !showEmpty) {
        return null;
      }

      const value = attrData?.value || '—';
      const unit = attrData?.unit;
      const title = attrData?.template?.title || name;

      return {
        name,
        title,
        value: withUnits && unit ? `${value} ${unit}` : value,
        unit
      };
    })
    .filter(Boolean) as Array<{ name: string; title: string; value: string; unit?: string }>;
}

/**
 * Создает контекст для интерполяции из данных рендеринга
 */
export function createInterpolationContext(data: Record<string, any>, attr: AttributeDict): InterpolationContext {
  return { data, attr };
}

/**
 * Валидирует шаблонную строку на корректность синтаксиса
 */
export function validateTemplate(template: string): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // Проверяем парность скобок
  const openBraces = (template.match(/\{\{/g) || []).length;
  const closeBraces = (template.match(/\}\}/g) || []).length;
  
  if (openBraces !== closeBraces) {
    errors.push('Несоответствие количества открывающих и закрывающих скобок');
  }

  // Проверяем корректность выражений
  const expressions = template.match(/\{\{\s*([^}]+)\s*\}\}/g) || [];
  
  for (const expr of expressions) {
    const content = expr.replace(/\{\{\s*|\s*\}\}/g, '');
    
    // Проверяем, что выражение не пустое
    if (!content.trim()) {
      errors.push('Найдено пустое выражение {{}}');
      continue;
    }

    // Проверяем корректность пути (только буквы, цифры, точки, подчеркивания)
    if (!/^[a-zA-Z_][a-zA-Z0-9_.]*$/.test(content.trim())) {
      errors.push(`Некорректное выражение: ${expr}`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Извлекает все переменные из шаблонной строки
 */
export function extractTemplateVariables(template: string): string[] {
  const variables: string[] = [];
  const expressions = template.match(/\{\{\s*([^}]+)\s*\}\}/g) || [];
  
  for (const expr of expressions) {
    const content = expr.replace(/\{\{\s*|\s*\}\}/g, '').trim();
    if (content && !variables.includes(content)) {
      variables.push(content);
    }
  }

  return variables;
}

/**
 * Проверяет, содержит ли шаблон переменные определенного типа
 */
export function hasVariableType(template: string, type: 'attr' | 'data'): boolean {
  const variables = extractTemplateVariables(template);
  return variables.some(variable => variable.startsWith(`${type}.`));
}

/**
 * Форматирует значение атрибута для отображения
 */
export function formatAttributeValue(
  value: string, 
  unit?: string | null, 
  withUnit = false
): string {
  if (!value || value === '—') {
    return '—';
  }

  if (withUnit && unit) {
    return `${value} ${unit}`;
  }

  return value;
}

/**
 * Создает fallback контент для случаев отсутствия шаблона
 */
export function createFallbackContent(
  type: 'h1' | 'h2' | 'description',
  entityName: string,
  entityType: 'category' | 'part' | 'catalogItem'
): string {
  const typeLabels = {
    category: 'Категория',
    part: 'Запчасть',
    catalogItem: 'Артикул'
  };

  switch (type) {
    case 'h1':
      return entityName || `${typeLabels[entityType]} без названия`;
    case 'h2':
      return `${typeLabels[entityType]}: ${entityName || 'Без названия'}`;
    case 'description':
      return `Подробная информация о ${typeLabels[entityType].toLowerCase()}: ${entityName || 'без названия'}`;
    default:
      return '';
  }
}
