"use client";

import React from 'react';
import type { RenderData } from '@/types/templates';
import { CategoryPageRenderer } from './CategoryTemplateRenderer';
import { PartPageRenderer } from './PartTemplateRenderer';
import { CatalogItemPageRenderer } from './CatalogItemTemplateRenderer';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertTriangle, Info } from 'lucide-react';

/**
 * Компонент для fallback рендеринга когда шаблон отсутствует или произошла ошибка
 */
export function FallbackRenderer({ renderData }: { renderData: RenderData }) {
  // Определяем тип данных по структуре
  const getEntityType = (): 'category' | 'part' | 'catalogItem' | 'unknown' => {
    if ('category' in renderData) return 'category';
    if ('part' in renderData) return 'part';
    if ('item' in renderData) return 'catalogItem';
    return 'unknown';
  };

  const entityType = getEntityType();

  // Показываем предупреждение о fallback режиме
  const FallbackWarning = () => (
    <Card className="border-amber-200 bg-amber-50 mb-6">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-amber-800">
          <Info className="h-4 w-4" />
          Режим по умолчанию
        </CardTitle>
        <CardDescription className="text-amber-700">
          Для этой страницы не настроен шаблон отображения. Используется стандартный вид.
        </CardDescription>
      </CardHeader>
    </Card>
  );

  // Рендерим в зависимости от типа сущности
  switch (entityType) {
    case 'category':
      return (
        <div>
          <FallbackWarning />
          <CategoryPageRenderer renderData={renderData} />
        </div>
      );

    case 'part':
      return (
        <div>
          <FallbackWarning />
          <PartPageRenderer renderData={renderData} />
        </div>
      );

    case 'catalogItem':
      return (
        <div>
          <FallbackWarning />
          <CatalogItemPageRenderer renderData={renderData} />
        </div>
      );

    default:
      return <UnknownEntityFallback />;
  }
}

/**
 * Компонент для случаев когда тип сущности не определен
 */
function UnknownEntityFallback() {
  return (
    <div className="text-center py-12">
      <Card className="border-red-200 bg-red-50 max-w-md mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-800 justify-center">
            <AlertTriangle className="h-5 w-5" />
            Ошибка отображения
          </CardTitle>
          <CardDescription className="text-red-700">
            Не удалось определить тип данных для отображения страницы.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-red-600">
            Пожалуйста, обратитесь к администратору сайта.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * Компонент для отображения ошибок загрузки данных
 */
export function DataLoadingError({ error, retry }: { error: string; retry?: () => void }) {
  return (
    <div className="text-center py-12">
      <Card className="border-red-200 bg-red-50 max-w-md mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-800 justify-center">
            <AlertTriangle className="h-5 w-5" />
            Ошибка загрузки данных
          </CardTitle>
          <CardDescription className="text-red-700">
            Произошла ошибка при загрузке данных страницы.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-sm text-red-600">{error}</p>
          {retry && (
            <button
              onClick={retry}
              className="w-full px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
            >
              Попробовать снова
            </button>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * Компонент для отображения состояния загрузки
 */
export function LoadingFallback() {
  return (
    <div className="space-y-6 animate-pulse">
      {/* Заголовок */}
      <div className="space-y-2">
        <div className="h-8 bg-gray-200 rounded w-3/4"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
      </div>

      {/* Изображение */}
      <div className="h-64 bg-gray-200 rounded-lg"></div>

      {/* Контент */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <div className="space-y-4">
            <div className="h-6 bg-gray-200 rounded w-1/3"></div>
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-5/6"></div>
              <div className="h-4 bg-gray-200 rounded w-4/6"></div>
            </div>
          </div>
        </div>
        
        <div className="space-y-4">
          <div className="h-32 bg-gray-200 rounded"></div>
          <div className="h-24 bg-gray-200 rounded"></div>
        </div>
      </div>
    </div>
  );
}

/**
 * Компонент для случаев когда данные не найдены
 */
export function NotFoundFallback({ entityType }: { entityType: 'category' | 'part' | 'catalogItem' }) {
  const labels = {
    category: 'Категория',
    part: 'Запчасть',
    catalogItem: 'Артикул'
  };

  return (
    <div className="text-center py-12">
      <Card className="border-gray-200 bg-gray-50 max-w-md mx-auto">
        <CardHeader>
          <CardTitle className="text-gray-800">
            {labels[entityType]} не найдена
          </CardTitle>
          <CardDescription className="text-gray-600">
            Запрашиваемая {labels[entityType].toLowerCase()} не существует или была удалена.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <button
            onClick={() => window.history.back()}
            className="w-full px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
          >
            Вернуться назад
          </button>
        </CardContent>
      </Card>
    </div>
  );
}
