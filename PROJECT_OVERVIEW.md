### Ключевая концепция: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs. Реализация

В основе нашей системы лежит разделение двух сущностей: **"Эталонной" детали** и **"Товарной позиции"**.

1.  **`Part` (Эталонная деталь / Группа взаимозаменяемости)**
    *   **Что это?** Это абстрактное представление детали, которое объединяет все её полные или частичные аналоги от разных производителей. Например, "Сальник коленвала пере<PERSON><PERSON><PERSON> для двигателя XYZ".
    *   **Характеристики (`PartAttribute`):** Атрибуты этой детали хранятся в нормализованном, "идеальном" виде. Например, внутренний диаметр строго `30.00` мм.
    *   **Цель:** Служить "эталоном" для сравнения и поиска аналогов.

2.  **`CatalogItem` (Товарная позиция / Артикул)**
    *   **Что это?** Это конкретный товар от конкретного производителя с его уникальным артикулом (SKU). Например, сальник от производителя SKF с артикулом `12345-ABC`.
    *   **Характеристики (`CatalogItemAttribute`):** Атрибуты хранятся "как есть", в том виде, в котором их указал производитель. Например, внутренний диаметр может быть `30.01` мм, а материал указан как `"TC"`.
    *   **Цель:** Представлять реальные товары, которые можно купить.

3.  **`PartApplicability` (Связь "Эталон-Реализация")**
    *   Эта модель связывает `CatalogItem` с `Part`, указывая, что данный артикул является аналогом для этой группы взаимозаменяемости.
    *   Содержит метаданные о качестве связи, например, `accuracy` (`EXACT_MATCH` — точный аналог, `MATCH_WITH_NOTES` — аналог с нюансами).

### Динамические Атрибуты

Вместо жестко заданных полей (ширина, высота и т.д.), мы используем гибкую систему атрибутов.

*   **`AttributeTemplate` (Шаблон атрибута):** Это "справочник" всех возможных характеристик в системе. Он определяет название атрибута ("Внутренний диаметр"), тип данных (`NUMBER`, `STRING`), единицу измерения (`MM`, `INCH`) и правила валидации.
*   **`PartAttribute` / `CatalogItemAttribute`:** Это конкретные *значения* атрибутов, привязанные к "Эталону" или "Товарной позиции" на основе шаблона.

### Логика Сопоставления Аналогов

Система автоматически сопоставляет `CatalogItem` с `Part` на основе атрибутов:

*   **Для числовых атрибутов (`NUMBER`):** Сравнение происходит с учётом поля `tolerance` (допуск) из `AttributeTemplate`. Если разница значений укладывается в допуск, они считаются эквивалентными.
*   **Для строковых атрибутов (`STRING`):** Используются группы синонимов.
    *   **`AttributeSynonymGroup` и `AttributeSynonym`:** Эти модели позволяют объединять разные строковые значения в одну логическую группу. Например, для атрибута "Материал уплотнения" значения "TC", "SC", "BABSL" могут быть объединены в одну группу "Стандартные типы", что позволит системе понять их эквивалентность.

### Техника и Применимость

*   **`EquipmentModel` (Модель техники):** Сущность для описания конкретной техники, например, "Экскаватор CAT 320D".
*   **`EquipmentApplicability`:** Связывает `Part` (группу деталей) с `EquipmentModel`, показывая, какие детали подходят к какой технике.

### Прочие важные модели

*   **`Brand` (Бренд):** Справочник производителей техники и запчастей.
*   **`PartCategory` (Категория запчастей):** Иерархический каталог для организации `Part` (например, Двигатель -> Система смазки).
*   **`MatchingProposal` (Предложение на сопоставление):** Когда система находит потенциальный аналог, она создает "предложение", которое попадает в очередь на проверку и утверждение инженером.
*   **`MediaAsset` (Медиафайлы):** Единая модель для управления всеми загруженными файлами (изображения, PDF) и их связями с другими сущностями.
