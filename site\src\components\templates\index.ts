// Экспорт всех компонентов системы шаблонов

// Основные компоненты рендеринга
export { TemplateRenderer, withTemplateErrorBoundary, useTemplateRenderer } from './TemplateRenderer';

// Компоненты для рендеринга категорий
export { CategoryTemplateRenderer, CategoryPageRenderer } from './CategoryTemplateRenderer';

// Компоненты для рендеринга запчастей
export { PartTemplateRenderer, PartPageRenderer } from './PartTemplateRenderer';

// Компоненты для рендеринга каталожных позиций
export { CatalogItemTemplateRenderer, CatalogItemPageRenderer } from './CatalogItemTemplateRenderer';

// Fallback компоненты
export { 
  FallbackRenderer, 
  DataLoadingError, 
  LoadingFallback, 
  NotFoundFallback 
} from './FallbackRenderer';

// Типы
export type {
  TemplateKind,
  AttributeListConfig,
  CategoryTemplateConfig,
  PartTemplateConfig,
  CatalogItemTemplateConfig,
  PageTemplate,
  AttributeData,
  AttributeDict,
  CategoryRenderData,
  PartRenderData,
  CatalogItemRenderData,
  RenderData,
  TemplateRendererProps,
  CategoryTemplateRendererProps,
  PartTemplateRendererProps,
  CatalogItemTemplateRendererProps,
  InterpolationResult,
  InterpolationContext,
  AttributeRenderOptions,
  PageSEOData
} from '@/types/templates';

// Утилиты
export {
  interpolateTemplate,
  interpolate,
  getAttributeValue,
  renderAttributesList,
  createInterpolationContext,
  validateTemplate,
  extractTemplateVariables,
  hasVariableType,
  formatAttributeValue,
  createFallbackContent
} from '@/lib/template-utils';
